import logging
from typing import List

import sentry_sdk

from holmatro_customer_portal.schemas.opensearch_schemas import FilterConfig
from holmatro_customer_portal.schemas.response_schema import SearchFiltersReq
from holmatro_customer_portal.utils.str_utils import str_is_numeric

logger = logging.getLogger(__name__)


class OpenSearchRequestMapper:
    """
    Mapper class for converting various filter formats to OpenSearch FilterConfig objects.
    """

    @staticmethod
    def map_filters_to_opensearch(filters: SearchFiltersReq) -> List[FilterConfig]:
        """
        Converts dictionary filters from SearchFiltersReq to a list of FilterConfig objects
        suitable for OpenSearch.

        Args:
            filters: A SearchFiltersReq object containing the filters' dictionary.

        Returns:
            A list of FilterConfig objects, or None if no filters are provided.
        """
        filter_configs: List[FilterConfig] = []
        for attribute_id, filter_value in filters.filters.items():
            # Checkbox filters
            if isinstance(filter_value, list):
                is_numeric = all(str_is_numeric(value) for value in filter_value)
                filter_configs.append(
                    FilterConfig(
                        attribute_id=attribute_id,
                        is_numeric_value=is_numeric,
                        values=filter_value,
                    )
                )
            # Range filters
            elif isinstance(filter_value, str) and "-" in filter_value:
                try:
                    min_val, max_val = map(float, filter_value.split("-"))
                    filter_configs.append(
                        FilterConfig(
                            attribute_id=attribute_id,
                            is_numeric_value=True,
                            min_value=min_val,
                            max_value=max_val,
                        )
                    )
                except ValueError as e:
                    logger.exception(f"Malformed range filter value '{filter_value}': {e}")
                    sentry_sdk.capture_exception(e, extras={"attribute_id": attribute_id, "filter_value": filter_value})
                    # For now, we'll skip malformed range filters
            else:
                # Treat single string values as a list with one item
                is_numeric = str_is_numeric(filter_value)
                filter_configs.append(
                    FilterConfig(
                        attribute_id=attribute_id,
                        is_numeric_value=is_numeric,
                        values=[filter_value],
                    )
                )
        return filter_configs
