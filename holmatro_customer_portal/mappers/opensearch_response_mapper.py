"""
OpenSearch Response Mapper Module

This module handles the conversion of OpenSearch responses into application-specific data structures.
OpenSearch is a distributed search and analytics engine that provides powerful search capabilities
and data aggregations for our product catalog.

Key OpenSearch Concepts Used:
- **Hits**: The actual search results (products) returned by OpenSearch
- **Aggregations**: Statistical summaries of data that help build filters/facets
- **Buckets**: Groups of documents that share common values (e.g., all products with color "red")
- **Facets**: User-facing filters derived from aggregations (e.g., "Color", "Weight", "Brand")
- **Nested Fields**: Complex data structures within documents (e.g., product attributes)

The mapper transforms OpenSearch's technical response format into user-friendly product listings
and filter options that can be displayed in the frontend catalog interface.
"""

import re
from typing import Any, Dict, List

from holmatro_customer_portal.database.enums import DBFilterType, LanguageEnum
from holmatro_customer_portal.database.models import DBFilter, User
from holmatro_customer_portal.schemas.opensearch_schemas import OpenSearchSearchResponse
from holmatro_customer_portal.schemas.response_schema import SearchFiltersReq
from holmatro_customer_portal.services.attribute_translation_service import AttributeTranslationService
from holmatro_customer_portal.services.filter_configuration_service import FilterConfigurationService
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.enums import SelectionType
from holmatro_customer_portal.utils.str_utils import str_is_numeric
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import (
    Facets,
    Filter,
    FilterAttribute,
    FilterSettings,
    Navigation,
    Product,
    ProductsResponse,
    ResponseProperties,
)


class OpenSearchResponseMapper:
    """
    Converts OpenSearch responses into application-specific product and filter data structures.

    This mapper serves as a bridge between OpenSearch's technical response format and our
    application's business logic. It handles two main transformations:

    1. **Product Mapping**: Converts OpenSearch "hits" (search results) into Product objects
    2. **Filter/Facet Mapping**: Converts OpenSearch "aggregations" into user-friendly filters

    OpenSearch Aggregations Explained:
    - OpenSearch aggregations are like SQL GROUP BY operations that summarize data
    - They tell us things like "how many products have color=red" or "what's the price range"
    - We use these statistics to build interactive filters for users

    Example OpenSearch aggregation structure:
    ```
    "aggregations": {
        "filterable_attributes": {           # Main aggregation container
            "attribute_ids": {               # Group by attribute ID (e.g., color, weight)
                "buckets": [                 # Each bucket = one attribute type
                    {
                        "key": 100,          # Attribute ID for "color"
                        "values_string": {   # Possible color values
                            "buckets": [
                                {"key": "red", "doc_count": 15},    # 15 red products
                                {"key": "blue", "doc_count": 8}     # 8 blue products
                            ]
                        }
                    }
                ]
            }
        }
    }
    ```
    """

    def __init__(self, session: Session):
        self.attribute_translation_service = AttributeTranslationService(session)
        self.filter_configuration_service = FilterConfigurationService(session)

    def map_to_products_response(
        self, opensearch_response: OpenSearchSearchResponse, page_number: int, page_size: int
    ) -> ProductsResponse:
        """
        Convert OpenSearch search results to a simple product listing.

        This method extracts the core search results (hits) from OpenSearch and converts
        them into Product objects, along with pagination metadata. It does NOT include
        filters/facets - use map_to_navigation_response() for that.

        Args:
            opensearch_response: Raw response from OpenSearch containing hits and metadata
            page_number: Current page number for pagination
            page_size: Number of items per page

        Returns:
            ProductsResponse: Contains product list and pagination info, no filters
        """
        products, properties = self._extract_products_and_pagination(opensearch_response, page_number, page_size)

        return ProductsResponse(items=products, properties=properties, redirects=[])

    def map_to_navigation_response(
        self,
        opensearch_response: OpenSearchSearchResponse,
        page_number: int,
        page_size: int,
        user: User,
        category_id: int | None = None,
        filters: SearchFiltersReq | None = None,
    ) -> Navigation:
        """
        Convert OpenSearch response to a full navigation interface with products AND filters.

        This is the main method for catalog pages that need both search results and
        interactive filters. It processes:
        1. Search hits → Product objects (same as map_to_products_response)
        2. Aggregations → Filter/Facet objects for user interaction

        The filters are built from OpenSearch aggregations, which are statistical summaries
        of the data (e.g., "15 products are red, 8 are blue"). These become clickable
        filter options in the UI.

        Args:
            opensearch_response: Raw OpenSearch response with hits and aggregations
            page_number: Current page for pagination
            page_size: Items per page
            user: Current user (needed for language and system type)
            category_id: Current category (determines which filters to show)
            filters: Currently selected filters from user (to mark as selected)

        Returns:
            Navigation: Complete interface with products, pagination, and interactive filters
        """
        # Extract products and pagination info from search hits
        products, properties = self._extract_products_and_pagination(opensearch_response, page_number, page_size)

        # Build interactive filters from OpenSearch aggregations (if available)
        facets = []
        if (
            category_id is not None
            and opensearch_response.aggregations
            and "filterable_attributes" in opensearch_response.aggregations
        ):
            facets = self._convert_opensearch_aggregations_to_filters(
                opensearch_response.aggregations, filters, user, category_id
            )

        return Navigation(items=products, properties=properties, redirects=[], facets=facets)

    def _extract_products_and_pagination(
        self, opensearch_response: OpenSearchSearchResponse, page_number: int, page_size: int
    ) -> tuple[List[Product], ResponseProperties]:
        """
        Extract product data and pagination info from OpenSearch hits.

        OpenSearch returns search results in a "hits" object that contains:
        - total: How many documents matched the search
        - hits: Array of actual documents (our products)

        This method processes both pieces to create our application's Product objects
        and pagination metadata.

        Args:
            opensearch_response: Raw OpenSearch response
            page_number: Current page number
            page_size: Items per page

        Returns:
            Tuple of (products_list, pagination_properties)
        """
        # OpenSearch wraps results in a "hits" container
        hits_container = opensearch_response.hits or {}
        total_hits_info = hits_container.get("total", {})

        # Extract total count (can be int or dict depending on OpenSearch version)
        total_count = self._extract_total_count_from_opensearch_format(total_hits_info)

        # Convert OpenSearch documents to our Product objects
        actual_hits = hits_container.get("hits", [])
        products = self._convert_opensearch_documents_to_products(actual_hits)

        # Build pagination metadata
        properties = self._create_pagination_properties(total_count, page_number, page_size)

        return products, properties

    def _convert_opensearch_aggregations_to_filters(
        self,
        aggregations: Dict[str, Any],
        user_selected_filters: SearchFiltersReq | None,
        user: User,
        category_id: int,
    ) -> List[Facets | Filter]:
        """
        Transform OpenSearch aggregations into user-friendly filter options.

        This is the core method that converts OpenSearch's statistical data into
        interactive filters for the frontend. The process involves several steps:

        1. **Filter Configuration**: Only show filters that are configured for this category
        2. **Aggregation Processing**: Convert OpenSearch buckets into filter options
        3. **User Selection**: Mark currently selected filters as active
        4. **Sorting**: Order filters according to admin configuration

        OpenSearch Aggregations Structure:
        - Aggregations contain "buckets" - groups of documents with common values
        - Each bucket has a "key" (the value) and "doc_count" (how many products)
        - We convert these into clickable filter options

        Args:
            aggregations: Raw aggregation data from OpenSearch
            user_selected_filters: Currently active filters from user's request
            user: Current user (for language and permissions)
            category_id: Current category (determines which filters to show)

        Returns:
            List of Filter objects ready for frontend display
        """
        category_id_str = str(category_id)

        # Step 1: Get admin configuration for which filters to show in this category
        configured_filters = self.filter_configuration_service.get_filters_for_category(
            category_id_str, user.system_type
        )
        filter_config_lookup = {db_filter.attribute_id: db_filter for db_filter in configured_filters}
        configured_attribute_ids = set(filter_config_lookup.keys())

        # Step 2: Convert OpenSearch aggregation buckets into base filter objects
        filters_by_url_key = self._extract_filters_from_opensearch_buckets(
            aggregations, user.language.code, configured_attribute_ids, filter_config_lookup
        )

        # Step 3: Mark user's currently selected filters as active
        if user_selected_filters and user_selected_filters.filters:
            self._mark_selected_filter_options(filters_by_url_key, user_selected_filters.filters)

        # Step 4: Sort filters according to admin configuration
        sorted_filters = self._sort_filters_by_position(filters_by_url_key, filter_config_lookup)

        return sorted_filters

    def _extract_filters_from_opensearch_buckets(
        self,
        aggregations: Dict[str, Any],
        user_language_code: str,
        configured_attribute_ids: set[int],
        filter_config_map: Dict[int, DBFilter],
    ) -> Dict[str, Filter]:
        """
        Extract filter options from OpenSearch aggregation buckets.

        OpenSearch aggregations group products by attribute values into "buckets".
        For example, a "color" attribute might have buckets like:
        - {"key": "red", "doc_count": 15}    # 15 red products
        - {"key": "blue", "doc_count": 8}    # 8 blue products

        This method converts these statistical buckets into user-friendly filter options.

        Args:
            aggregations: Raw OpenSearch aggregation data
            user_language_code: User's language for translations
            configured_attribute_ids: Which attributes are configured for this category
            filter_config_map: Admin configuration for each filter

        Returns:
            Dictionary mapping URL keys to Filter objects
        """
        filters_by_url_key: Dict[str, Filter] = {}

        # Navigate to the attribute buckets in OpenSearch response structure
        # Structure: aggregations -> filterable_attributes -> attribute_ids -> buckets
        attribute_buckets = aggregations["filterable_attributes"]["attribute_ids"]["buckets"]

        # Get comprehensive attribute translations (metadata + value translations)
        attribute_translations = self._fetch_attribute_translations(attribute_buckets, user_language_code)

        # Process each attribute bucket (e.g., color, weight, brand)
        for attribute_bucket in attribute_buckets:
            # Each bucket represents one attribute type (identified by attribute_id)
            attribute_id = int(attribute_bucket["key"])

            # Skip attributes that aren't configured for this category
            if configured_attribute_ids and attribute_id not in configured_attribute_ids:
                continue

            # Convert this attribute bucket into a Filter object
            filter_obj = self._build_filter_from_attribute_bucket(
                attribute_bucket, attribute_translations, filter_config_map, user_language_code
            )
            if filter_obj:
                filters_by_url_key[filter_obj.facet_settings.url_key] = filter_obj

        return filters_by_url_key

    def _build_filter_from_attribute_bucket(
        self,
        attribute_bucket: Dict[str, Any],
        attribute_translations: dict,
        filter_config_map: Dict[int, DBFilter],
        user_language_code: str,
    ) -> Filter | None:
        """
        Convert a single OpenSearch attribute bucket into a Filter object.

        An attribute bucket contains all the data for one filter type (e.g., "color").
        It includes:
        - The attribute ID and name
        - All possible values (buckets) for that attribute
        - Statistics for numeric attributes (min, max, avg)

        This method handles both string attributes (like colors, brands) and
        numeric attributes (like weights, dimensions).

        Args:
            attribute_bucket: Single attribute data from OpenSearch aggregation
            attribute_translations: Translated names for attributes
            filter_config_map: Admin configuration for filters

        Returns:
            Filter object ready for frontend, or None if invalid
        """
        # Extract basic attribute information
        attribute_id = int(attribute_bucket["key"])
        raw_attribute_name = attribute_bucket["attribute_name"]["buckets"][0]["key"]

        # Extract attribute type for intelligent formatting (handle missing gracefully)
        attribute_type = attribute_bucket["attribute_type"]["buckets"][0]["key"]

        attribute_info = attribute_translations.get(attribute_id, {})

        # Check if this attribute is configured as a filter
        filter_config = filter_config_map.get(attribute_id)
        if not filter_config:
            return None

        # Prepare display information
        url_key = str(attribute_id)  # Use attribute id as url key
        translated_title = attribute_info.get("title")
        unit = attribute_info.get("unit")
        selection_type = filter_config.filter_type.value

        # Extract filter options based on data type
        filter_options = self._extract_filter_options_from_bucket(
            attribute_bucket, filter_config, user_language_code, attribute_translations, attribute_type
        )
        if not filter_options:
            return None

        # Format the display title with unit if available
        display_title = self._format_filter_title(translated_title or raw_attribute_name, unit)

        # Create the filter configuration
        filter_settings = FilterSettings(
            facet_id=attribute_id,
            selection_type=selection_type,
            url_key=url_key,
            is_collapsed=False,
            title=display_title,
            is_no_of_results_visible=filter_config.show_nr_of_results,
        )

        return Filter(facet_settings=filter_settings, attributes=filter_options)

    @staticmethod
    def _extract_filter_options_from_bucket(
        attribute_bucket: Dict[str, Any],
        filter_config: DBFilter,
        user_language_code: str,
        comprehensive_translations: dict,
        attribute_type: str,
    ) -> List[FilterAttribute]:
        """
        Extract filter options from an OpenSearch attribute bucket.

        OpenSearch stores attribute values differently based on data type:
        - String values: stored in "values_string" with individual buckets
        - Numeric values (stats): stored in "values_numeric_stats" with statistical aggregations
        - Numeric values (terms): stored in "values_numeric_terms" with individual buckets

        Args:
            attribute_bucket: OpenSearch bucket for one attribute
            filter_config: Configuration for this filter
            user_language_code: User's language for translations

        Returns:
            List of FilterAttribute objects representing selectable options
        """
        key_stats = "values_numeric_stats"  # For slider
        key_numeric = "values_numeric_terms"  # For numeric checkbox options
        key_string = "values_string"  # For string-based checkbox options

        attribute_id = attribute_bucket["key"]
        match filter_config.filter_type:
            case DBFilterType.CHECKBOX:
                filter_attributes = []

                # Handle string-based checkbox options
                if key_string in attribute_bucket and attribute_bucket[key_string]["buckets"]:
                    # If English, use raw values for both title and display_name
                    if user_language_code == LanguageEnum.ENGLISH.value:
                        filter_attributes = [
                            FilterAttribute(
                                title=bucket["key"],
                                display_name=bucket["key"],
                                no_of_results=bucket["doc_count"] if filter_config.show_nr_of_results else None,
                                is_selected=False,
                            )
                            for bucket in attribute_bucket[key_string]["buckets"]
                        ]
                    else:
                        # For non-English, use pre-fetched translations
                        attribute_translations = comprehensive_translations.get(attribute_id, {})
                        value_translations = attribute_translations.get("value_translations", {})

                        for bucket in attribute_bucket[key_string]["buckets"]:
                            raw_value = bucket["key"]
                            translated_value = value_translations.get(raw_value, raw_value)
                            filter_attributes.append(
                                FilterAttribute(
                                    title=raw_value,  # Keep original English value
                                    display_name=translated_value,  # Use translated value for display
                                    no_of_results=bucket["doc_count"] if filter_config.show_nr_of_results else None,
                                    is_selected=False,
                                )
                            )
                    return OpenSearchResponseMapper._sort_checkbox_values(filter_attributes)

                # Handle numeric checkbox options
                elif key_numeric in attribute_bucket and attribute_bucket[key_numeric]["buckets"]:

                    filter_attributes = []
                    for bucket in attribute_bucket[key_numeric]["buckets"]:
                        value = bucket["key"]
                        # Format the value based on attribute type
                        if attribute_type.lower() == "integer":
                            # Display as integer if it's an integer type and the value is a whole number
                            display_value = str(int(value))
                        else:
                            # Display as float for non-integer types or decimal values
                            display_value = str(value)

                        filter_attributes.append(
                            FilterAttribute(
                                title=display_value,
                                no_of_results=bucket["doc_count"] if filter_config.show_nr_of_results else None,
                                display_name=display_value,
                                is_selected=False,
                            )
                        )

                    return OpenSearchResponseMapper._sort_checkbox_values(filter_attributes)
            case DBFilterType.SLIDER:
                # Handle numeric attributes (weights, dimensions, etc.)
                if key_stats in attribute_bucket and attribute_bucket[key_stats]["count"] > 0:
                    numeric_stats = attribute_bucket[key_stats]

                    if attribute_type.lower() == "integer":
                        # Display as integers for integer types
                        min_value = str(int(numeric_stats["min"]))
                        max_value = str(int(numeric_stats["max"]))
                    else:
                        # Display as floats for non-integer types
                        min_value = str(numeric_stats["min"])
                        max_value = str(numeric_stats["max"])

                    # For numeric attributes, we provide min/max range options
                    return [
                        FilterAttribute(title=min_value, no_of_results=None, display_name=min_value, is_selected=False),
                        FilterAttribute(title=max_value, no_of_results=None, display_name=max_value, is_selected=False),
                    ]

        # No valid data found
        return []

    def map_attribute_aggregation_to_filter_attributes(
        self,
        attribute_id: int,
        aggregation_results: OpenSearchSearchResponse,
        filter_config: DBFilter,
        user_language_code: str,
    ) -> List[FilterAttribute]:
        """
        Map OpenSearch attribute aggregation results directly to FilterAttribute objects.

        This method takes the raw aggregation results from OpenSearchClient.get_attribute_aggregation()
        and converts them to a list of FilterAttribute objects using the existing mapping logic.
        """
        if not aggregation_results.aggregations:
            return []
        filtered_data = aggregation_results.aggregations["filterable_attributes"]["filter_by_attribute_id"]
        filtered_data["key"] = attribute_id  # Add attribute ID to mimic OpenSearch bucket structure

        attribute_type = filtered_data["attribute_type"]["buckets"][0]["key"]

        # Use the existing method with correct parameters (no translations for this use case)
        attribute_translations: dict = {}
        return self._extract_filter_options_from_bucket(
            filtered_data, filter_config, user_language_code, attribute_translations, attribute_type
        )

    @staticmethod
    def _format_filter_title(base_title: str, unit: str | None) -> str:
        """
        Format a filter title with optional unit information.

        Args:
            base_title: Base attribute name (e.g., "weight", "color")
            unit: Optional unit string (e.g., "kg", "mm")

        Returns:
            Formatted title ready for display
        """
        formatted_title = base_title.capitalize()

        if unit:
            # If unit already has parentheses, append as-is
            if re.search(r"\(.*\)", unit):
                formatted_title = f"{formatted_title} {unit}"
            else:
                # Otherwise, wrap in parentheses
                formatted_title = f"{formatted_title} ({unit})"

        return formatted_title

    def _mark_selected_filter_options(
        self, filters_by_url_key: Dict[str, Filter], user_filters: Dict[str, Any]
    ) -> None:
        """
        Mark user's currently selected filter options as active.

        When a user has applied filters (e.g., selected "red" color), we need to mark
        those options as selected in the filter objects so the frontend can display
        them as active/checked.

        Args:
            filters_by_url_key: Dictionary of available filters
            user_filters: User's currently selected filter values
        """
        for filter_url_key, selected_values in user_filters.items():
            # Skip if this filter doesn't exist in our available filters
            if filter_url_key not in filters_by_url_key:
                continue

            # Normalize the selected values to a consistent format
            normalized_values = self._normalize_filter_values(selected_values)
            if not normalized_values:
                continue

            filter_obj = filters_by_url_key[filter_url_key]

            # Apply selections based on filter type
            if filter_obj.facet_settings.selection_type == SelectionType.SLIDER.value:
                # Slider filters use range values (e.g., "10-50")
                self._mark_slider_selection(filter_obj, normalized_values[0])
            else:
                # Checkbox filters use individual values (e.g., ["red", "blue"])
                self._mark_checkbox_selections(filter_obj, normalized_values)

    @staticmethod
    def _normalize_filter_values(filter_values: Any) -> List[str]:
        """
        Convert filter values to a consistent list of strings format.

        User filter values can come in various formats:
        - Single string: "red"
        - List of values: ["red", "blue"]
        - Mixed types: [1, "blue", None]

        Args:
            filter_values: Raw filter values from user request

        Returns:
            List of string values, empty list if invalid
        """
        if isinstance(filter_values, str):
            return [filter_values]
        elif isinstance(filter_values, list):
            return [str(val) for val in filter_values if val]
        return []

    def _mark_slider_selection(self, filter_obj: Filter, range_value: str) -> None:
        """
        Mark slider filter selection (for numeric ranges).

        Slider filters represent numeric ranges like "10-170" for weight.
        We need to add the selected range endpoints to the filter options
        and mark them as selected.

        Args:
            filter_obj: The filter to update
            range_value: Range string like "10-170"
        """
        if "-" not in range_value:
            return

        range_parts = range_value.split("-")
        if len(range_parts) != 2:
            return

        start_value = self._normalize_numeric_string(range_parts[0])
        end_value = self._normalize_numeric_string(range_parts[1])

        # Add selected range values to the beginning of the options list
        for value in [start_value, end_value]:
            if value and not self._is_value_already_selected(filter_obj.attributes, value):
                filter_obj.attributes.insert(
                    0,
                    FilterAttribute(title=value, no_of_results=None, display_name=value, is_selected=True),
                )

    @staticmethod
    def _mark_checkbox_selections(filter_obj: Filter, selected_values: List[str]) -> None:
        """
        Mark checkbox filter selections (for discrete values).

        Checkbox filters have individual options like colors or brands.
        We need to mark the selected options and ensure all selected values
        are present in the options list.

        Args:
            filter_obj: The filter to update
            selected_values: List of selected option values
        """
        selected_set = set(selected_values)
        updated_options = []
        processed_titles = set()

        # Process existing filter options
        for option in filter_obj.attributes:
            is_selected = option.title in selected_set
            updated_options.append(
                FilterAttribute(
                    title=option.title,
                    no_of_results=option.no_of_results,
                    display_name=option.display_name,
                    is_selected=is_selected,
                )
            )
            processed_titles.add(option.title)
            if is_selected:
                selected_set.discard(option.title)

        filter_obj.attributes = updated_options

    def _is_value_already_selected(self, attributes: List[FilterAttribute], value: str) -> bool:
        """
        Check if a filter value is already marked as selected.

        This prevents duplicate entries when adding selected values to filter options.
        Handles numeric normalization to match values like "10.0" with "10".

        Args:
            attributes: Current filter options
            value: Value to check for

        Returns:
            True if value is already selected, False otherwise
        """
        normalized_value = self._normalize_numeric_string(value)
        return any(
            self._normalize_numeric_string(attr.title) == normalized_value and attr.is_selected for attr in attributes
        )

    def _fetch_attribute_translations(self, attribute_buckets: list, user_language_code: str) -> dict:
        # Extract attribute values that might need translation
        attribute_values_by_id = {}

        for bucket in attribute_buckets:
            attribute_id = int(bucket["key"])

            # Extract string values that might need translation
            key_string = "values_string"
            if key_string in bucket and bucket[key_string]["buckets"]:
                raw_values = [value_bucket["key"] for value_bucket in bucket[key_string]["buckets"]]
                attribute_values_by_id[attribute_id] = raw_values
            else:
                # Even if no values to translate, we still want attribute metadata
                attribute_values_by_id[attribute_id] = []

        # Use the comprehensive method to get both types of translations
        return self.attribute_translation_service.get_attribute_translations(attribute_values_by_id, user_language_code)

    @staticmethod
    def _extract_total_count_from_opensearch_format(total_hits: dict[str, Any] | int) -> int:
        """
        Extract total count from OpenSearch response format.

        OpenSearch can return total hits in different formats depending on version:
        - Newer versions: {"value": 150, "relation": "eq"}
        - Older versions: 150 (direct integer)

        Args:
            total_hits: Total hits data from OpenSearch response

        Returns:
            Total number of matching documents
        """
        if isinstance(total_hits, dict):
            return int(total_hits.get("value", 0))
        return int(total_hits)

    def _convert_opensearch_documents_to_products(self, opensearch_hits: list[dict[str, Any]]) -> list[Product]:
        """
        Convert OpenSearch document hits to Product objects.

        Each "hit" in OpenSearch represents one product document. The actual product
        data is stored in the "_source" field of each hit.

        Args:
            opensearch_hits: List of hit objects from OpenSearch response

        Returns:
            List of Product objects ready for frontend display
        """
        products = []
        for hit in opensearch_hits:
            # Extract the actual document data from the hit
            document_source = hit.get("_source", {})
            product = self._convert_single_document_to_product(document_source)
            products.append(product)
        return products

    @staticmethod
    def _normalize_numeric_string(numeric_string: str) -> str:
        """
        Normalize numeric strings for consistent comparison.

        Converts values like "10.0" to "10" for cleaner display and matching.
        Non-numeric strings are returned unchanged.

        Args:
            numeric_string: String that might represent a number

        Returns:
            Normalized string representation
        """
        try:
            float_value = float(numeric_string)
            # If it's a whole number, return as integer string
            if float_value == int(float_value):
                return str(int(float_value))
            return str(float_value)
        except ValueError:
            # Not a number, return as-is
            return numeric_string

    @staticmethod
    def _convert_single_document_to_product(opensearch_document: dict[str, Any]) -> Product:
        """
        Convert a single OpenSearch document to a Product object.

        OpenSearch documents contain all the indexed product data, but for the
        basic Product object we only need the ID. Product names and other details
        are fetched separately from the database to get proper translations.

        Args:
            opensearch_document: Raw document data from OpenSearch

        Returns:
            Product object with basic information
        """
        # Extract product ID and convert to string for item number
        product_id = opensearch_document.get("product_id")
        item_number = str(product_id) if product_id is not None else ""

        # Title is placeholder - actual translations come from database queries
        # This keeps the search fast while allowing proper localization
        placeholder_title = "placeholder"

        return Product(
            itemno=item_number, title=placeholder_title, brand="Holmatro"  # Default brand - could be made configurable
        )

    @staticmethod
    def _create_pagination_properties(total_count: int, page_number: int, page_size: int) -> ResponseProperties:
        """
        Create pagination metadata for the response.

        Calculates pagination information based on total results and page settings.
        This helps the frontend display page navigation controls.

        Args:
            total_count: Total number of matching products
            page_number: Current page number (1-based)
            page_size: Number of products per page

        Returns:
            ResponseProperties with pagination metadata
        """
        # Calculate total pages (round up division)
        total_pages = (total_count + page_size - 1) // page_size

        return ResponseProperties(
            no_of_items=total_count,
            page_size=page_size,
            no_of_pages=total_pages,
            current_page=page_number,
            category_id=None,  # Could be extracted from search context if needed
        )

    @staticmethod
    def _sort_filters_by_position(
        filters_by_url_key: Dict[str, Filter], filter_config_map: Dict[int, DBFilter]
    ) -> List[Facets | Filter]:
        """
        Sort filters according to admin-configured display order.

        Administrators can configure the order in which filters appear in the UI.
        This method sorts the filters based on their configured position values.
        Filters without explicit positions are placed at the end.

        Args:
            filters_by_url_key: Dictionary of available filters
            filter_config_map: Admin configuration including position settings

        Returns:
            List of filters sorted by display position
        """
        filters_with_positions = []

        for filter_obj in filters_by_url_key.values():
            attribute_id = filter_obj.facet_settings.facet_id
            filter_config = filter_config_map.get(attribute_id)

            # Use configured position, or high number for unconfigured filters
            display_position = filter_config.position if filter_config else 999999

            filters_with_positions.append((display_position, filter_obj))

        # Sort by position and extract just the filter objects
        filters_with_positions.sort(key=lambda item: item[0])
        return [filter_obj for position, filter_obj in filters_with_positions]

    @staticmethod
    def _sort_checkbox_values(values: list[FilterAttribute]) -> list[FilterAttribute]:
        """
        Sort checkbox filter values alphabetically ASC, based on the title value.
        Numeric values will be sorted numerically.

        Args:
            values: List of FilterAttribute objects to sort

        Returns:
            Sorted list of FilterAttribute objects
        """
        if all(str_is_numeric(attr.title) for attr in values):
            # If all titles are numeric, sort numerically
            return sorted(values, key=lambda attr: float(attr.title) if attr.title else 0.0)

        # Otherwise, sort alphabetically by title (case-insensitive)
        return sorted(values, key=lambda attr: attr.title.lower() if attr.title else "")
