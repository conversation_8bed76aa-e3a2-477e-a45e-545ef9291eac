import logging
from enum import Enum
from os import getenv
from typing import Any, Optional


class Env(Enum):
    """
    Simple wrapper class around `getenv()` that prevents the use of stringly typed env var keys/names.
    Also helps by catching errors early when env vars are not defined by raising an Exception.
    """

    # All defined env vars for this project. You can use a tuple value to specify a
    # default value to be returned, when the env var has not been set.
    LOGGING_LEVEL = ("LOGGING_LEVEL", logging.getLevelName(logging.INFO))
    SQLALCHEMY_LOGGING_LEVEL = ("SQLALCHEMY_LOGGING_LEVEL", logging.getLevelName(logging.ERROR))

    STAGE = ("STAGE", "prod")
    DEFAULT_REGION = ("DEFAULT_REGION", "eu-west-1")
    DATABASE_NAME = "DATABASE_NAME"
    DATABASE_USER = "DATABASE_USER"
    DATABASE_PASSWORD = "DATABASE_PASSWORD"
    DATABASE_PORT = ("DATABASE_PORT", "3306")
    DATABASE_WRITE_HOST = ("DATABASE_WRITE_HOST", "mysql-service")

    HOLMATRO_ASSETS_BUCKET_NAME = "HOLMATRO_ASSETS_BUCKET_NAME"

    UVICORN_PORT = ("UVICORN_PORT", "8000", int)
    UVICORN_SERVER_HOST = ("UVICORN_SERVER_HOST", "")

    TWEAKWISE_INSTANCE_KEY = "TWEAKWISE_INSTANCE_KEY"
    TWEAKWISE_TOKEN = "TWEAKWISE_TOKEN"

    B2C_TENANT_ID = "B2C_TENANT_ID"
    B2C_BASE_URL = ("B2C_BASE_URL", "https://myhmb2c.b2clogin.com")

    MY_HM_BASE_URL = ("MY_HM_BASE_URL", "https://myhmapi.holmatro.com")
    MY_HM_SUBSCRIPTION_TOKEN = "MY_HM_SUBSCRIPTION_TOKEN"

    JWT_ENCODING_KEY = "JWT_ENCODING_KEY"
    ALLOW_ANONYMOUS_USER = ("ALLOW_ANONYMOUS_USER", True, bool)
    JWT_ISSUER_ANONYMOUS_USAGE = ("JWT_ISSUER_ANONYMOUS_USAGE", "https://myportal.holmatro.com")

    SSM_HOLMATRO_PORTAL_API_KEY_NAME = "SSM_HOLMATRO_PORTAL_API_KEY_NAME"
    API_KEY_NAME = ("API_KEY_NAME", "access-token")

    AZURE_API_URL = ("AZURE_API_URL", "")
    AZURE_API_KEY = ("AZURE_API_KEY", "")

    RABBITMQ_BROKER_URL = "RABBITMQ_BROKER_URL"

    SYNC_FORCE_PROPOSITION_ID = ("SYNC_FORCE_PROPOSITION_ID", "210c1c88-5d82-46e3-bbf1-9f6263c70e47")

    SENTRY_ENV = ("SENTRY_ENV", "production")
    SENTRY_URL_INCL_TOKEN = ("SENTRY_URL_INCL_TOKEN", "")
    SENTRY_TRACING_SAMPLE_RATE = ("SENTRY_TRACING_SAMPLE_RATE", 0.7, float)
    SENTRY_PROFILES_SAMPLE_RATE = ("SENTRY_PROFILES_SAMPLE_RATE", 0.2, float)

    REQUEST_TIMEOUT_SECONDS = ("REQUEST_TIMEOUT_SECONDS", "30")

    CACHE_TTL = ("CACHE_TTL", "10800", int)  # 3 hours
    CACHE_SIZE = ("CACHE_SIZE", "1000", int)

    IS_HOLMATRO_CATALOG = ("IS_HOLMATRO_CATALOG", "false", bool)

    OPENSEARCH_FUZZY_SEARCH_ENABLED = ("OPENSEARCH_FUZZY_SEARCH_ENABLED", "false", bool)
    OPENSEARCH_HOST = ("OPENSEARCH_HOST", "opensearch")
    OPENSEARCH_PORT = ("OPENSEARCH_PORT", 9200, int)
    OPENSEARCH_USERNAME = ("OPENSEARCH_USERNAME", "")
    OPENSEARCH_PASSWORD = ("OPENSEARCH_PASSWORD", "")
    OPENSEARCH_USE_SSL = ("OPENSEARCH_USE_SSL", "true", bool)
    OPENSEARCH_INDEX_NAME = "OPENSEARCH_INDEX_NAME"

    MIN_PRODUCTS_PROPOSITION_THRESHOLD = ("MIN_PRODUCTS_THRESHOLD", 500, int)
    MAX_AUTOCOMPLETE_SUGGESTIONS = ("MAX_AUTOCOMPLETE_SUGGESTIONS", 5, int)
    IMPORT_BATCH_SIZE = ("IMPORT_BATCH_SIZE", 10, int)

    def get(self, default: Any = None) -> Any:
        """
        Retrieves the env var's value. Value is determined in the following order:
        1. Value that's that defined in the OS's environment
        2. The default value that's specified for this method
        3. The default value that's defined in the tuple
        4. If value is still None, then it will raise an exception.
        """
        if (value := self.get_non_raise(default)) is None:
            raise Exception(f"Env var '{self._get_value()}' not set for current environment.")
        return self._convert_to_type(value)

    def get_non_raise(self, default: Any = None) -> Optional[Any]:
        """
        Functionally the same as `get()`, but will return None instead of raising an Exception.
        Use this method sparingly as getting None values where they're not expected may
        result in unintentional behaviour!
        """
        return self._one_of(getenv(self._get_value()), default, self._get_default())

    def _get_value(self) -> str:
        """Gets the string name"""
        if isinstance(self.value, tuple):
            return str(self.value[0])
        return str(self.value)

    def _get_default(self) -> Optional[Any]:
        """Gets the default value when specified or None otherwise"""
        if isinstance(self.value, tuple):
            return self.value[1]
        return None

    def _one_of(self, *args: Any) -> Optional[Any]:
        # Return first arg that is not None
        for arg in args:
            if arg is not None:
                return arg
        return None

    def _convert_to_type(self, value: Any) -> Any:
        """Converts the value to the type that's specified in the tuple"""
        if isinstance(self.value, tuple) and len(self.value) == 3:
            converter = self.value[2]

            # Special handling for boolean conversion from strings
            if converter is bool and isinstance(value, str):
                return value.lower() in ("true", "1")

            return converter(value)
        return value
