from typing import Annotated

import typer
from typer import Typer

from holmatro_customer_portal.scripts.index_products import ProductIndexer
from holmatro_customer_portal.scripts.insert_filter_templates import import_filters_and_templates
from holmatro_customer_portal.utils.database import construct_db_write_url, session_creator
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.open_search.opensearch_client import OpenSearchClient

app: Typer = Typer(help="Command line tool for the Holmatro Customer Portal")


@app.command()
def reindex(
    dirty: Annotated[bool, typer.Option(help="Only reindex dirty records, otherwise full reindex")] = False
) -> None:
    opensearch_client = OpenSearchClient(
        is_fuzzy_search_enabled=False,
        index_name=Env.OPENSEARCH_INDEX_NAME.get(),
        max_autocomplete_suggestions=Env.MAX_AUTOCOMPLETE_SUGGESTIONS.get(),
    )
    ProductIndexer(opensearch_client=opensearch_client).index_products(
        session=session_creator(construct_db_write_url())(), force_full_reindex=not dirty
    )


@app.command(
    short_help="Re-import filter templates and their associated filters into the database.",
    help=(
        "Re-import filter templates and their associated filters into the database. The function can be safely called "
        "multiple times, as it truncates the existing filter templates and filters tables before inserting new data."
    ),
)
def reimport_filters() -> None:
    import_filters_and_templates()


if __name__ == "__main__":
    app()
