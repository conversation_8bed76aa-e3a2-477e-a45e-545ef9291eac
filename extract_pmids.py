#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to extract all unique pmid values from a nested JSON structure.
The JSON contains categories that can have nested subcategories, and products with pmid values.
"""

import json
import sys
from typing import Set, Dict, Any, List


def extract_pmids_from_products(products: List[Dict[str, Any]]) -> Set[str]:
    """Extract pmid values from a list of products."""
    pmids = set()
    if products:
        for product in products:
            if isinstance(product, dict) and 'pmid' in product:
                pmids.add(product['pmid'])
    return pmids


def extract_pmids_from_categories(categories: List[Dict[str, Any]]) -> Set[str]:
    """Recursively extract pmid values from categories and their subcategories."""
    pmids = set()
    
    if not categories:
        return pmids
    
    for category in categories:
        if not isinstance(category, dict):
            continue
            
        # Extract pmids from products in this category
        if 'products' in category:
            pmids.update(extract_pmids_from_products(category['products']))
        
        # Recursively extract pmids from subcategories
        if 'categories' in category:
            pmids.update(extract_pmids_from_categories(category['categories']))
    
    return pmids


def extract_all_pmids(data: Dict[str, Any]) -> Set[str]:
    """Extract all unique pmid values from the entire JSON structure."""
    pmids = set()
    
    # Check if there's a top-level categories array
    if 'categories' in data:
        pmids.update(extract_pmids_from_categories(data['categories']))
    
    # Also check if there are products at the top level
    if 'products' in data:
        pmids.update(extract_pmids_from_products(data['products']))
    
    return pmids


def main():
    """Main function to read JSON file and extract unique pmids."""
    filename = 'temp.json'
    
    try:
        # Read the JSON file
        with open(filename, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        # Extract all unique pmids
        unique_pmids = extract_all_pmids(data)
        
        # Sort the pmids for consistent output
        sorted_pmids = sorted(unique_pmids)
        
        # Print results
        print(f"Found {len(sorted_pmids)} unique pmid values:")
        print("-" * 50)
        
        for pmid in sorted_pmids:
            print(pmid)
        
        # Also save to a file for easy reference
        output_filename = 'unique_pmids.txt'
        with open(output_filename, 'w', encoding='utf-8') as output_file:
            output_file.write(f"Found {len(sorted_pmids)} unique pmid values:\n")
            output_file.write("-" * 50 + "\n")
            for pmid in sorted_pmids:
                output_file.write(pmid + "\n")
        
        print(f"\nResults also saved to: {output_filename}")
        
    except FileNotFoundError:
        print(f"Error: File '{filename}' not found.")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in file '{filename}': {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
