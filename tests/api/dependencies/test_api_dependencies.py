from unittest.mock import MagicMock, patch

import pytest

MODULE_PATH = "holmatro_customer_portal.api.dependencies"


@pytest.mark.parametrize(
    "is_holmatro_env_setting",
    [
        pytest.param(True, id="Env_IS_HOLMATRO_CATALOG_is_True"),
        pytest.param(False, id="Env_IS_HOLMATRO_CATALOG_is_False"),
    ],
)
@patch(f"{MODULE_PATH}.Env.IS_HOLMATRO_CATALOG.get")
@patch(f"{MODULE_PATH}.Env.OPENSEARCH_INDEX_NAME.get")
@patch(f"{MODULE_PATH}.Env.MAX_AUTOCOMPLETE_SUGGESTIONS.get")
@patch(f"{MODULE_PATH}.OpenSearchClient", autospec=True)
@patch(f"{MODULE_PATH}.HolmatroCatalogRepository", autospec=True)
@patch(f"{MODULE_PATH}.TweakwiseClient", autospec=True)
@patch(f"{MODULE_PATH}.TweakwiseCatalogRepository", autospec=True)
def test_get_catalog_repository_selects_correct_repository(
    mock_tweakwise_repo_class,
    mock_tweakwise_client_class,
    mock_holmatro_repo_class,
    mock_opensearch_client_class,
    mock_env_get_max_autocomplete_suggestions,
    mock_env_get_opensearch_index_name,
    mock_env_get_is_holmatro,
    is_holmatro_env_setting,
):
    from holmatro_customer_portal.api.dependencies import get_catalog_repository

    mock_env_get_is_holmatro.return_value = is_holmatro_env_setting
    mock_env_get_opensearch_index_name.return_value = "products"
    mock_env_get_max_autocomplete_suggestions.return_value = 5

    mock_db = MagicMock()
    repository = get_catalog_repository(db=mock_db)

    mock_env_get_is_holmatro.assert_called_once()

    if is_holmatro_env_setting:
        mock_opensearch_client_class.assert_called_once_with(
            is_fuzzy_search_enabled=False, index_name="products", max_autocomplete_suggestions=5
        )
        mock_holmatro_repo_class.assert_called_once_with(
            db=mock_db, opensearch_client=mock_opensearch_client_class.return_value
        )
        assert repository == mock_holmatro_repo_class.return_value

        mock_tweakwise_repo_class.assert_not_called()
        mock_tweakwise_client_class.assert_not_called()
    else:
        mock_tweakwise_client_class.assert_called_once_with()
        mock_tweakwise_repo_class.assert_called_once_with(tweakwise_client=mock_tweakwise_client_class.return_value)
        assert repository == mock_tweakwise_repo_class.return_value
        mock_holmatro_repo_class.assert_not_called()
        mock_opensearch_client_class.assert_not_called()
