from unittest.mock import Mock, patch

import pytest

from holmatro_customer_portal.utils.open_search.opensearch_client import OpenSearchClient


class TestOpenSearchClientFuzziness:
    """Test the dual approach: phrase matching for exact matches + fuzzy matching for typo correction."""

    @pytest.fixture
    def mock_opensearch(self):
        """Mock the OpenSearch client."""
        with patch("holmatro_customer_portal.utils.open_search.opensearch_client.OpenSearch") as mock:
            mock_instance = Mock()
            mock.return_value = mock_instance
            mock_instance.search.return_value = {"hits": {"total": {"value": 0}, "hits": []}}
            yield mock_instance

    def test_dual_query_structure_enabled(self, mock_opensearch):
        """Test that both phrase and fuzzy queries are used when fuzziness is enabled."""
        # Arrange
        client = OpenSearchClient(is_fuzzy_search_enabled=True, index_name="test_index", max_autocomplete_suggestions=5)

        # Act
        client.search_products(search_query="test query")

        # Assert
        mock_opensearch.search.assert_called_once()
        call_args = mock_opensearch.search.call_args
        body = call_args[1]["body"]

        # Check that both phrase and fuzzy queries are used
        bool_query = body["query"]["bool"]
        should_clauses = bool_query["should"]

        # Find phrase and fuzzy multi_match queries
        phrase_queries = [
            clause
            for clause in should_clauses
            if "multi_match" in clause and clause["multi_match"].get("type") == "phrase"
        ]
        fuzzy_queries = [
            clause
            for clause in should_clauses
            if "multi_match" in clause and clause["multi_match"].get("type") == "best_fields"
        ]

        assert len(phrase_queries) > 0, "Should have phrase queries"
        assert len(fuzzy_queries) > 0, "Should have fuzzy queries"

        # Verify fuzzy queries have fuzziness
        for fuzzy_query in fuzzy_queries:
            assert "fuzziness" in fuzzy_query["multi_match"]
            assert fuzzy_query["multi_match"]["fuzziness"] == "AUTO"

    def test_phrase_query_structure_disabled(self, mock_opensearch):
        """Test that phrase queries are used regardless of fuzziness setting."""
        # Arrange
        client = OpenSearchClient(
            is_fuzzy_search_enabled=False, index_name="test_index", max_autocomplete_suggestions=5
        )

        # Act
        client.search_products(search_query="test query")

        # Assert
        mock_opensearch.search.assert_called_once()
        call_args = mock_opensearch.search.call_args
        body = call_args[1]["body"]

        # Check that phrase queries are used
        bool_query = body["query"]["bool"]
        should_clauses = bool_query["should"]

        # Find the multi_match phrase query for regular fields
        multi_match_query = None
        for clause in should_clauses:
            if "multi_match" in clause:
                multi_match_query = clause["multi_match"]
                break

        assert multi_match_query is not None
        assert multi_match_query["type"] == "phrase"
        assert "fuzziness" not in multi_match_query

    def test_query_structure_with_phrase_matching(self, mock_opensearch):
        """Test the complete query structure with phrase matching."""
        # Arrange
        client = OpenSearchClient(is_fuzzy_search_enabled=True, index_name="test_index", max_autocomplete_suggestions=5)

        # Act
        client.search_products(search_query="hydraulic cutter", page_number=2, page_size=20)

        # Assert
        mock_opensearch.search.assert_called_once()
        call_args = mock_opensearch.search.call_args
        body = call_args[1]["body"]

        # Verify complete structure
        assert body["from"] == 20  # (page_number - 1) * page_size
        assert body["size"] == 20
        assert "sort" in body

        # Verify bool query structure with nested and multi_match queries
        bool_query = body["query"]["bool"]
        should_clauses = bool_query["should"]
        assert len(should_clauses) > 0

        # Find the multi_match phrase query for regular fields
        multi_match_query = None
        for clause in should_clauses:
            if "multi_match" in clause:
                multi_match_query = clause["multi_match"]
                break

        assert multi_match_query is not None
        assert multi_match_query["query"] == "hydraulic cutter"
        assert multi_match_query["type"] == "phrase"
        assert "fuzziness" not in multi_match_query  # Phrase queries don't support fuzziness
        assert len(multi_match_query["fields"]) > 0

    def test_query_structure_phrase_matching_consistent(self, mock_opensearch):
        """Test that phrase matching is consistent regardless of fuzziness setting."""
        # Arrange
        client = OpenSearchClient(
            is_fuzzy_search_enabled=False, index_name="test_index", max_autocomplete_suggestions=5
        )

        # Act
        client.search_products(search_query="hydraulic cutter")

        # Assert
        mock_opensearch.search.assert_called_once()
        call_args = mock_opensearch.search.call_args
        body = call_args[1]["body"]

        # Verify bool query structure uses phrase matching
        bool_query = body["query"]["bool"]
        should_clauses = bool_query["should"]

        # Find the multi_match phrase query for regular fields
        multi_match_query = None
        for clause in should_clauses:
            if "multi_match" in clause:
                multi_match_query = clause["multi_match"]
                break

        assert multi_match_query is not None
        assert multi_match_query["query"] == "hydraulic cutter"
        assert multi_match_query["type"] == "phrase"
        assert "fuzziness" not in multi_match_query
        assert len(multi_match_query["fields"]) > 0

    def test_no_search_query_uses_match_all(self, mock_opensearch):
        """Test that match_all query is used when no search query is provided."""
        # Arrange
        client = OpenSearchClient(is_fuzzy_search_enabled=True, index_name="test_index", max_autocomplete_suggestions=5)

        # Act
        client.search_products()

        # Assert
        mock_opensearch.search.assert_called_once()
        call_args = mock_opensearch.search.call_args
        body = call_args[1]["body"]

        # Should be a match_all query when no search query is provided
        assert body["query"] == {"match_all": {}}

    def test_nested_queries_use_both_phrase_and_fuzzy_matching(self, mock_opensearch):
        """Test that nested queries use both phrase and fuzzy matching when fuzziness is enabled."""
        # Arrange
        client = OpenSearchClient(is_fuzzy_search_enabled=True, index_name="test_index", max_autocomplete_suggestions=5)

        # Act
        client.search_products(search_query="cutter")

        # Assert
        mock_opensearch.search.assert_called_once()
        call_args = mock_opensearch.search.call_args
        body = call_args[1]["body"]

        # Verify bool query structure
        bool_query = body["query"]["bool"]
        should_clauses = bool_query["should"]

        # Find nested queries
        nested_queries = [clause for clause in should_clauses if "nested" in clause]
        assert len(nested_queries) > 0

        # Should have both phrase and fuzzy nested queries
        phrase_nested_queries = [clause for clause in nested_queries if "match_phrase" in clause["nested"]["query"]]
        fuzzy_nested_queries = [
            clause
            for clause in nested_queries
            if "match" in clause["nested"]["query"] and "match_phrase" not in clause["nested"]["query"]
        ]

        assert len(phrase_nested_queries) > 0, "Should have phrase nested queries"
        assert len(fuzzy_nested_queries) > 0, "Should have fuzzy nested queries"

        # Verify fuzzy queries have fuzziness
        for fuzzy_query in fuzzy_nested_queries:
            nested_match = fuzzy_query["nested"]["query"]["match"]
            for field, match_config in nested_match.items():
                if isinstance(match_config, dict):
                    assert "fuzziness" in match_config
                    assert match_config["fuzziness"] == "AUTO"

    def test_nested_queries_consistent_phrase_matching(self, mock_opensearch):
        """Test that nested queries consistently use phrase matching regardless of fuzziness setting."""
        # Arrange
        client = OpenSearchClient(
            is_fuzzy_search_enabled=False, index_name="test_index", max_autocomplete_suggestions=5
        )

        # Act
        client.search_products(search_query="cutter")

        # Assert
        mock_opensearch.search.assert_called_once()
        call_args = mock_opensearch.search.call_args
        body = call_args[1]["body"]

        # Verify bool query structure
        bool_query = body["query"]["bool"]
        should_clauses = bool_query["should"]

        # Find nested queries and verify they use match_phrase consistently
        nested_queries = [clause for clause in should_clauses if "nested" in clause]
        assert len(nested_queries) > 0

        for nested_query in nested_queries:
            # Should be match_phrase, not match
            assert "match_phrase" in nested_query["nested"]["query"]
            nested_match_phrase = nested_query["nested"]["query"]["match_phrase"]

            # Each nested query should use match_phrase (no fuzziness)
            for field, match_config in nested_match_phrase.items():
                if isinstance(match_config, dict):
                    assert "fuzziness" not in match_config
                else:
                    # Should be a simple string value for phrase queries
                    assert isinstance(match_config, str)
