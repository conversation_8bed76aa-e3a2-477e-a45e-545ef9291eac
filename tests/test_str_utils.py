import pytest

from holmatro_customer_portal.utils.str_utils import str_is_numeric


class TestStrIsNumeric:
    @pytest.mark.parametrize(
        "input_value,expected",
        [
            # Valid numeric strings
            ("123", True),
            ("0", True),
            ("456.789", True),
            ("0.5", True),
            ("123.0", True),
            ("1.0", True),
            # Invalid strings
            ("abc", False),
            ("12a", False),
            ("a12", False),
            ("12.34.56", False),  # Multiple dots
            ("", False),  # Empty string
            (" ", False),  # Space
            ("12 34", False),  # Space in between
            ("12.34 ", False),  # Trailing space
            (" 12.34", False),  # Leading space
            (".", False),  # Just a dot
            ("..", False),  # Multiple dots only
            ("12.", True),  # Trailing dot (valid float)
            (".123", True),  # Leading dot (valid float)
            ("12-34", False),  # Hyphen
            ("+123", False),  # Plus sign
            ("-123", False),  # Minus sign
            ("1e10", False),  # Scientific notation
            ("inf", False),  # Infinity
            ("nan", False),  # Not a number
        ],
    )
    def test_str_is_numeric(self, input_value, expected):
        """Test str_is_numeric function with various inputs."""
        assert str_is_numeric(input_value) == expected
