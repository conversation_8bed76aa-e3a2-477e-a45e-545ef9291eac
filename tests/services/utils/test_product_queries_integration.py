from unittest.mock import Mock

import pytest

from holmatro_customer_portal.database.enums import AssetType
from holmatro_customer_portal.database.models import Asset, AssetResource, CategoryAssociation, ProductNameTranslation
from holmatro_customer_portal.services.utils.product_queries import get_query_products
from tests.factories import CategoryFactory, LanguageFactory, ProductFactory, UserFactory
from tests.fixtures.database import session


class TestGetQueryProductsIntegration:
    """Integration tests for get_query_products with actual database operations."""

    @pytest.fixture(autouse=True)
    def _pass_fixtures(self, session):
        self.session = session

    def _create_base_test_data(self, category_id: str = "1234"):
        """Create basic test data: language, user, and category."""
        language = LanguageFactory.build(language_code="en")
        user = UserFactory.build(main_language=language)
        category = CategoryFactory.build(category_id=category_id)

        self.session.add_all([language, user, category])
        self.session.commit()

        return language, user, category

    def _create_product_with_assets(self, product_id: int, article_number: str, product_name: str, language_id: int):
        """Create a product with all required translations, assets, and resources."""
        product = ProductFactory.build(product_id=product_id, article_number=article_number)
        self.session.add(product)
        self.session.commit()

        # Create product name translation
        name_translation = ProductNameTranslation(product_id=product.id, language_id=language_id, value=product_name)

        # Create asset and asset resource
        asset = Asset(product_id=product.id, asset_type=AssetType.PRODUCT_MAIN_IMAGE)
        self.session.add_all([name_translation, asset])
        self.session.commit()

        resource = AssetResource(
            asset_id=asset.id, asset_type="HIGHRESOLUTION", url=f"url_{product_id}", file_name=f"file_{product_id}"
        )
        self.session.add(resource)
        self.session.commit()

        return product

    def _create_category_association(self, category_id: int, product_id: int, sort_order: int | None):
        """Create a CategoryAssociation with the specified sort order."""
        association = CategoryAssociation(category_id=category_id, product_id=product_id, product_sort_order=sort_order)
        self.session.add(association)
        self.session.commit()
        return association

    def _create_mock_products(self, product_ids: list[int]) -> list[Mock]:
        """Create mock products list for the function call."""
        return [Mock(item_no=f"PROD-{pid}") for pid in product_ids]

    def test_get_query_products_sorts_by_product_sort_order(self):
        """Test that get_query_products returns products sorted by CategoryAssociation.product_sort_order."""
        # Create base test data
        language, user, category = self._create_base_test_data("1234")

        # Create products with assets
        product1 = self._create_product_with_assets(101, "PROD001", "Product 1", language.id)
        product2 = self._create_product_with_assets(102, "PROD002", "Product 2", language.id)
        product3 = self._create_product_with_assets(103, "PROD003", "Product 3", language.id)

        # Create CategoryAssociations with specific sort orders (reverse order to test sorting)
        # product1 should be last (sort_order=3), product2 first (sort_order=1), product3 middle (sort_order=2)
        self._create_category_association(category.id, product1.id, 3)
        self._create_category_association(category.id, product2.id, 1)
        self._create_category_association(category.id, product3.id, 2)

        # Create mock products list and call the function
        mock_products = self._create_mock_products([101, 102, 103])
        result = get_query_products(mock_products, self.session, user, category_id="1234")

        # Verify results are ordered by product_sort_order (1, 2, 3)
        assert len(result) == 3
        assert result[0].tweakwise_id == 102  # product_sort_order = 1
        assert result[1].tweakwise_id == 103  # product_sort_order = 2
        assert result[2].tweakwise_id == 101  # product_sort_order = 3

        # Verify other fields are correctly populated
        assert result[0].product_name == "Product 2"
        assert result[1].product_name == "Product 3"
        assert result[2].product_name == "Product 1"

    def test_get_query_products_mixed_sort_orders(self):
        """Test complex scenario with mixed sort orders including duplicates, excluding NULLs."""
        # Create base test data
        language, user, category = self._create_base_test_data("9999")

        # Create 5 products with assets
        products = []
        for i in range(1, 6):
            product = self._create_product_with_assets(300 + i, f"MIX00{i}", f"Mixed Product {i}", language.id)
            products.append(product)

        # Create CategoryAssociations with mixed sort orders:
        # product1: sort_order=2, product2: sort_order=1, product3: sort_order=NULL,
        # product4: sort_order=1 (duplicate), product5: sort_order=NULL
        sort_orders = [2, 1, None, 1, None]
        for product, sort_order in zip(products, sort_orders):
            self._create_category_association(category.id, product.id, sort_order)

        # Create mock products list and call the function
        mock_products = self._create_mock_products([301, 302, 303, 304, 305])
        result = get_query_products(mock_products, self.session, user, category_id="9999")

        # Verify results: only products with non-NULL sort_order are returned
        assert len(result) == 5

        # First products should have sort_order=1 (products 2 and 4)
        # They should appear first in the results, but their order among themselves is not guaranteed
        first_two_ids = {result[0].tweakwise_id, result[1].tweakwise_id}
        assert first_two_ids == {302, 304}

        # Next product should have sort_order=2 (product 1)
        assert result[2].tweakwise_id == 301

        # Products with NULL sort_order (products 3 and 5) should be excluded

    def test_get_query_products_no_matching_products(self):
        """Test behavior when no products match the query."""
        # Create base test data (no products needed for this test)
        language, user, category = self._create_base_test_data("0000")

        # Create mock products list with non-existent product IDs and call the function
        mock_products = self._create_mock_products([999, 998])
        result = get_query_products(mock_products, self.session, user, category_id="0000")

        # Verify empty result
        assert len(result) == 0
