import uuid
from unittest.mock import Mock

from holmatro_customer_portal.database.enums import AssetType
from holmatro_customer_portal.services.utils.product_queries import get_query_products
from holmatro_customer_portal.services.utils.queries import group_assets
from tests.fixtures.database_models_fixture import mocked_user


def test_get_query_products_sort_order():
    tweakwise_products = [<PERSON><PERSON>(item_no="1-1"), <PERSON><PERSON>(item_no="1-3"), <PERSON><PERSON>(item_no="1-4"), <PERSON><PERSON>(item_no="1-2")]
    db_products = [<PERSON><PERSON>(tweakwise_id=1), <PERSON><PERSON>(tweakwise_id=2), <PERSON><PERSON>(tweakwise_id=3), <PERSON><PERSON>(tweakwise_id=4)]

    mock_db = Mock()

    mock_db.query().join().join().join().join().join().filter().group_by().filter().order_by().all.return_value = (
        db_products
    )

    result = get_query_products(tweakwise_products, mock_db, mocked_user, category_id="1234")

    assert len(tweakwise_products) == len(result)

    result_ids = [r.tweakwise_id for r in result]
    expected_ids = [1, 2, 3, 4]
    assert result_ids == expected_ids


class MockAlchemyAsset:
    def __init__(self, id, url, file_type, type, file_name, asset_size):
        self.id = id
        self.url = url
        self.file_type = file_type
        self.type = type
        self.asset_size = asset_size
        self.file_name = file_name


def test_group_assets():
    # Mock asset results
    asset_results = [
        MockAlchemyAsset(
            id=uuid.uuid4(),
            url="http://example.com/asset1.jpg",
            file_type="OTHER",
            asset_size=0.1,
            type=AssetType.PRODUCT_APPLICATION_SHOT,
            file_name="guide1",
        ),
        MockAlchemyAsset(
            id=uuid.uuid4(),
            url="http://example.com/asset2.jpg",
            file_type="OTHER",
            asset_size=0.1,
            type=AssetType.PRODUCT_CHARACTERISTICS,
            file_name="guide1",
        ),
        MockAlchemyAsset(
            id=uuid.uuid4(),
            url="http://example.com/asset3.jpg",
            file_type="OTHER",
            asset_size=0.1,
            type=AssetType.PRODUCT_MAIN_IMAGE,
            file_name="drawing1",
        ),
    ]

    # Call the function to test
    grouped_assets = group_assets(asset_results)

    # Verify that the assets are grouped correctly
    assert len(grouped_assets) == 2  # Assuming there are 2 unique file_names
    assert len(grouped_assets[0].asset_files) == 2  # Assuming 'guide1' has 2 assets
    assert len(grouped_assets[1].asset_files) == 1  # Assuming 'drawing1' has 1 asset
    assert grouped_assets[0].file_name == "guide1"  # Check if file names are correct
    assert grouped_assets[1].file_name == "drawing1"
